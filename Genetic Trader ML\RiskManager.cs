using System;
using cAlgo.API;
using cAlgo.API.Internals;

namespace GeneticTraderML.Services
{
    public class RiskManager
    {
        private readonly Symbol _symbol;
        private readonly double _riskPerTrade;
        private readonly double _maxDailyLoss;
        private readonly double _maxDrawdown;
        
        private double _dailyHighBalance;
        private DateTime _lastResetDate;

        public RiskManager(Symbol symbol, double riskPerTrade = 0.01, double maxDailyLoss = 0.02, double maxDrawdown = 0.1)
        {
            _symbol = symbol;
            _riskPerTrade = riskPerTrade;
            _maxDailyLoss = maxDailyLoss;
            _maxDrawdown = maxDrawdown;
            _lastResetDate = DateTime.Today;
            _dailyHighBalance = 0;
        }

        public double CalculatePositionSize(double accountEquity, double atr, double stopLossPips)
        {
            // Use ATR-based stop loss or fixed stop loss
            var stopLoss = stopLossPips > 0 ? stopLossPips : atr * 2;
            
            // Risk-based position sizing
            var riskAmount = accountEquity * _riskPerTrade;
            var positionSize = riskAmount / (stopLoss * _symbol.PipValue);
            
            // Normalize volume
            positionSize = _symbol.NormalizeVolumeInUnits(positionSize, RoundingMode.Down);
            
            // Apply minimum volume constraint
            if (positionSize < _symbol.VolumeInUnitsMin)
                positionSize = _symbol.VolumeInUnitsMin;
                
            return positionSize;
        }

        public bool CanOpenPosition(double currentBalance, double equity)
        {
            // Reset daily high balance if it's a new day
            if (DateTime.Today > _lastResetDate)
            {
                _dailyHighBalance = equity;
                _lastResetDate = DateTime.Today;
            }

            // Update daily high
            if (equity > _dailyHighBalance)
                _dailyHighBalance = equity;

            // Check daily loss limit
            var dailyDrawdown = (_dailyHighBalance - equity) / _dailyHighBalance;
            if (dailyDrawdown >= _maxDailyLoss)
                return false;

            // Check overall drawdown
            var totalDrawdown = (_dailyHighBalance - equity) / _dailyHighBalance;
            if (totalDrawdown >= _maxDrawdown)
                return false;

            return true;
        }

        public double GetStopLossPips(TradeType tradeType, double atr, double entryPrice)
        {
            // ATR-based stop loss (2x ATR)
            var atrStopLoss = atr * 2;
            
            // Minimum stop loss (10 pips)
            var minStopLoss = 10 / _symbol.PipSize;
            
            return Math.Max(atrStopLoss, minStopLoss);
        }

        public double GetTakeProfitPips(TradeType tradeType, double stopLossPips)
        {
            // 1.5:1 risk-reward ratio
            return stopLossPips * 1.5;
        }
    }
}