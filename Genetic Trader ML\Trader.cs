using System;
using System.Collections.Generic;
using GeneticTraderML.NeuralNetworks;
using cAlgo.API;

namespace GeneticTraderML.Models
{
    [Serializable]
    public class Trader
    {
        public NeuralNetwork Brain { get; private set; }
        public double Fitness { get; set; }
        public double Balance { get; set; }
        public double MaxDrawdown { get; private set; }
        public double PeakBalance { get; private set; }
        public int TotalTrades { get; private set; }
        public int WinningTrades { get; private set; }
        public int LosingTrades { get; private set; }
        public double TotalProfit { get; private set; }
        
        // Trading state
        public TradeType CurrentTradeType { get; set; }
        public double EntryPrice { get; set; }
        public double PositionSize { get; set; }
        
        private const double INITIAL_BALANCE = 10000;
        private readonly List<double> _equityHistory;

        public Trader()
        {
            var layers = new[] { 20, 16, 12, 4 }; // Input, Hidden1, Hidden2, Output
            Brain = new NeuralNetwork(0.001, layers);
            Reset();
            _equityHistory = new List<double>();
        }

        public double[] Think(double[] inputs)
        {
            try
            {
                return Brain.Run(inputs);
            }
            catch (Exception ex)
            {
                // Fallback to random decisions if network fails
                var random = new Random();
                return new[] 
                {
                    random.NextDouble(),
                    random.NextDouble(), 
                    random.NextDouble(),
                    random.NextDouble()
                };
            }
        }

        public TradingDecision InterpretOutput(double[] networkOutput)
        {
            const double confidenceThreshold = 0.6;
            
            double buySignal = networkOutput[0];
            double sellSignal = networkOutput[1];
            double closeSignal = networkOutput[2];
            double holdSignal = networkOutput[3];

            // Simple interpretation logic
            if (closeSignal > confidenceThreshold)
                return TradingDecision.CloseAll;
                
            if (buySignal > confidenceThreshold && buySignal > sellSignal && buySignal > holdSignal)
                return TradingDecision.Buy;
                
            if (sellSignal > confidenceThreshold && sellSignal > buySignal && sellSignal > holdSignal)
                return TradingDecision.Sell;
                
            return TradingDecision.Hold;
        }

        public void UpdateBalance(double profitLoss)
        {
            Balance += profitLoss;
            TotalProfit += profitLoss;
            
            if (profitLoss > 0)
                WinningTrades++;
            else if (profitLoss < 0)
                LosingTrades++;
                
            TotalTrades++;

            // Update drawdown
            if (Balance > PeakBalance)
                PeakBalance = Balance;
                
            var drawdown = (PeakBalance - Balance) / PeakBalance;
            if (drawdown > MaxDrawdown)
                MaxDrawdown = drawdown;

            _equityHistory.Add(Balance);
        }

        public void CalculateFitness()
        {
            if (TotalTrades == 0)
            {
                Fitness = 0;
                return;
            }

            double winRate = (double)WinningTrades / TotalTrades;
            double profitFactor = WinningTrades > 0 ? 
                (WinningTrades * 1.5) / Math.Max(1, LosingTrades * 0.8) : 0;
            
            double consistencyBonus = CalculateConsistencyBonus();
            double drawdownPenalty = Math.Max(0, MaxDrawdown * 10);

            Fitness = (winRate * 0.3) + 
                     (profitFactor * 0.3) + 
                     (consistencyBonus * 0.2) - 
                     (drawdownPenalty * 0.2);
        }

        private double CalculateConsistencyBonus()
        {
            if (_equityHistory.Count < 10) return 0;
            
            double avgReturn = TotalProfit / TotalTrades;
            double variance = 0;
            
            foreach (var equity in _equityHistory)
            {
                variance += Math.Pow(equity - avgReturn, 2);
            }
            
            double stdDev = Math.Sqrt(variance / _equityHistory.Count);
            return stdDev > 0 ? avgReturn / stdDev : 0; // Sharpe-like ratio
        }

        public void Reset()
        {
            Balance = INITIAL_BALANCE;
            PeakBalance = INITIAL_BALANCE;
            MaxDrawdown = 0;
            TotalTrades = 0;
            WinningTrades = 0;
            LosingTrades = 0;
            TotalProfit = 0;
            CurrentTradeType = TradeType.Buy; // Default
            EntryPrice = 0;
            PositionSize = 0;
            Fitness = 0;
            _equityHistory.Clear();
            _equityHistory.Add(INITIAL_BALANCE);
        }

        public Trader Clone()
        {
            var clone = new Trader();
            var weights = Brain.GetWeights();
            clone.Brain.SetWeights(weights);
            clone.Fitness = Fitness;
            return clone;
        }
    }

    public enum TradingDecision
    {
        Hold = 0,
        Buy = 1,
        Sell = 2,
        CloseAll = 3
    }
}