{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Genetic Trader ML/1.0.0": {"dependencies": {"VWAP": "1.0.0", "cTrader.Automate": "1.0.14", "Newtonsoft.Json": "13.0.0.0"}, "runtime": {"Genetic Trader ML.dll": {}}}, "cTrader.Automate/1.0.14": {"runtime": {"lib/net6.0/cAlgo.API.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "SSL Channel/1.0.0": {"dependencies": {"cTrader.Automate": "1.0.14"}, "runtime": {"SSL Channel.dll": {}}}, "VWAP/1.0.0": {"dependencies": {"cTrader.Automate": "1.0.14"}, "runtime": {"VWAP.dll": {}}}, "Newtonsoft.Json/13.0.0.0": {"runtime": {"Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}}}, "libraries": {"Genetic Trader ML/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "cTrader.Automate/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-eNwE7WL90MGBKb5MuLAtZLdQy0vxkI5EVhLWAQ9S83EAdYkGAzdccvGFLk2oqmtSGBtD+gEpyrJUW/ej4dI4jw==", "path": "ctrader.automate/1.0.14", "hashPath": "ctrader.automate.1.0.14.nupkg.sha512"}, "SSL Channel/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "VWAP/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Newtonsoft.Json/13.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}}}