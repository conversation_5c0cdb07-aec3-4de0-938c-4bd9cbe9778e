﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="cTrader.Automate" Version="1.*" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Indicators\SSL Channel\SSL Channel\SSL Channel.csproj" />
    <ProjectReference Include="..\..\..\Indicators\VWAP\VWAP\VWAP.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed">
      <HintPath>..\..\Genetic DNA ML\Genetic DNA ML\bin\Release\net6.0\Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed">
      <HintPath>..\..\Genetic Trader ML v2\Genetic Trader ML v2\bin\Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>