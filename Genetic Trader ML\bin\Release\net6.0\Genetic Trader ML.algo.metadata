[{"Flags": "Robot", "Guid": "00000000-0000-0000-0000-000000000000", "Group": "Custom", "FriendlyName": "GeneticTraderML", "ShortName": null, "TypeName": "cAlgo.Robots.GeneticTraderML", "AssemblyName": "Genetic Trader ML, Version=*******, Culture=neutral, PublicKeyToken=null", "TimeZone": "UTC", "IsOverlay": false, "IsPercentage": false, "ScalePrecision": null, "Levels": [], "DefaultSymbolName": null, "DefaultTimeFrame": null, "Video": null, "Lines": [], "Clouds": [], "Parameters": [{"DefaultValue": true, "ParameterType": "Boolean", "PropertyName": "TrainingMode", "FriendlyName": "Training Mode", "GroupName": null, "IsValueVisibleInTitle": true, "Description": ""}, {"MinValue": -2147483648, "MaxValue": 2147483647, "Step": 1, "DefaultValue": 50, "ParameterType": "Integer", "PropertyName": "PopulationSize", "FriendlyName": "Population Size", "GroupName": null, "IsValueVisibleInTitle": true, "Description": ""}, {"MinValue": -1.7976931348623157e+308, "MaxValue": 1.7976931348623157e+308, "Step": 0.1, "DefaultValue": 1.0, "ParameterType": "Double", "PropertyName": "RiskPerTrade", "FriendlyName": "Risk Per Trade %", "GroupName": null, "IsValueVisibleInTitle": true, "Description": ""}, {"MinValue": -1.7976931348623157e+308, "MaxValue": 1.7976931348623157e+308, "Step": 0.1, "DefaultValue": 2.0, "ParameterType": "Double", "PropertyName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FriendlyName": "Max Daily Loss %", "GroupName": null, "IsValueVisibleInTitle": true, "Description": ""}], "Capabilities": [{"CapabilityType": "FullTrust"}], "CustomAttributes": [], "Sets": [], "AddIndicatorsToChart": false, "AdditionalInfoUrl": null, "Bars": []}]