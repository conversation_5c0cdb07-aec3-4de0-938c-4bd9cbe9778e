{"version": 3, "targets": {"net6.0": {"cTrader.Automate/1.0.14": {"type": "package", "compile": {"lib/net6.0/cAlgo.API.dll": {}}, "runtime": {"lib/net6.0/cAlgo.API.dll": {}}, "build": {"build/cTrader.Automate.props": {}, "build/cTrader.Automate.targets": {}}}, "SSL Channel/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"cTrader.Automate": "1.0.0"}, "compile": {"bin/placeholder/SSL Channel.dll": {}}, "runtime": {"bin/placeholder/SSL Channel.dll": {}}}, "VWAP/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"cTrader.Automate": "1.0.0"}, "compile": {"bin/placeholder/VWAP.dll": {}}, "runtime": {"bin/placeholder/VWAP.dll": {}}}}}, "libraries": {"cTrader.Automate/1.0.14": {"sha512": "eNwE7WL90MGBKb5MuLAtZLdQy0vxkI5EVhLWAQ9S83EAdYkGAzdccvGFLk2oqmtSGBtD+gEpyrJUW/ej4dI4jw==", "type": "package", "path": "ctrader.automate/1.0.14", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/cTrader.Automate.props", "build/cTrader.Automate.targets", "ctrader.automate.1.0.14.nupkg.sha512", "ctrader.automate.nuspec", "eula.md", "icon.png", "lib/net40/cAlgo.API.dll", "lib/net40/cAlgo.API.xml", "lib/net6.0/cAlgo.API.dll", "lib/net6.0/cAlgo.API.xml", "tools/net472/Core.AlgoFormat.Compose.Reflection.dll", "tools/net472/Core.AlgoFormat.Writer.dll", "tools/net472/Core.AlgoFormat.dll", "tools/net472/Core.Domain.Primitives.dll", "tools/net472/Newtonsoft.Json.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Reflection.MetadataLoadContext.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/cTrader.Automate.Sdk.Tasks.dll", "tools/net6.0/Core.AlgoFormat.Compose.Reflection.dll", "tools/net6.0/Core.AlgoFormat.Writer.dll", "tools/net6.0/Core.AlgoFormat.dll", "tools/net6.0/Core.Connection.Protobuf.Common.dll", "tools/net6.0/Core.Domain.Primitives.dll", "tools/net6.0/Microsoft.Win32.SystemEvents.dll", "tools/net6.0/Newtonsoft.Json.dll", "tools/net6.0/System.Drawing.Common.dll", "tools/net6.0/System.Reflection.MetadataLoadContext.dll", "tools/net6.0/System.Security.Permissions.dll", "tools/net6.0/System.Windows.Extensions.dll", "tools/net6.0/cTrader.Automate.Sdk.Tasks.dll", "tools/net6.0/protobuf-net.Core.dll", "tools/net6.0/protobuf-net.dll", "tools/net6.0/runtimes/unix/lib/net6.0/System.Drawing.Common.dll", "tools/net6.0/runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "tools/net6.0/runtimes/win/lib/net6.0/System.Drawing.Common.dll", "tools/net6.0/runtimes/win/lib/net6.0/System.Windows.Extensions.dll"]}, "SSL Channel/1.0.0": {"type": "project", "path": "../../../Indicators/SSL Channel/SSL Channel/SSL Channel.csproj", "msbuildProject": "../../../Indicators/SSL Channel/SSL Channel/SSL Channel.csproj"}, "VWAP/1.0.0": {"type": "project", "path": "../../../Indicators/VWAP/VWAP/VWAP.csproj", "msbuildProject": "../../../Indicators/VWAP/VWAP/VWAP.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["SSL Channel >= 1.0.0", "VWAP >= 1.0.0", "cTrader.Automate >= 1.*"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Robots\\Genetic Trader ML\\Genetic Trader ML\\Genetic Trader ML.csproj", "projectName": "Genetic Trader ML", "projectPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Robots\\Genetic Trader ML\\Genetic Trader ML\\Genetic Trader ML.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Robots\\Genetic Trader ML\\Genetic Trader ML\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\SSL Channel\\SSL Channel\\SSL Channel.csproj": {"projectPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\SSL Channel\\SSL Channel\\SSL Channel.csproj"}, "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\VWAP\\VWAP\\VWAP.csproj": {"projectPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\VWAP\\VWAP\\VWAP.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"cTrader.Automate": {"target": "Package", "version": "[1.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.100\\RuntimeIdentifierGraph.json"}}}}