using System;
using System.Collections.Generic;
using System.Linq;

namespace GeneticTraderML.NeuralNetworks
{
    public class NeuralNetwork : IDisposable
    {
        private readonly Random _random;
        private readonly double _learningRate;
        
        public NetworkTopology Topology { get; private set; }
        public double Fitness { get; set; }

        public NeuralNetwork(double learningRate, int[] layers)
        {
            _random = new Random(Guid.NewGuid().GetHashCode());
            _learningRate = learningRate;
            Topology = new NetworkTopology(layers, _random);
            Fitness = 0;
        }

        public double[] Run(double[] inputs)
        {
            if (inputs.Length != Topology.Layers[0].Neurons.Length)
                throw new ArgumentException("Input size mismatch");

            // Set input layer
            for (int i = 0; i < inputs.Length; i++)
            {
                Topology.Layers[0].Neurons[i].Value = inputs[i];
            }

            // Forward propagation
            for (int layerIndex = 1; layerIndex < Topology.Layers.Length; layerIndex++)
            {
                var currentLayer = Topology.Layers[layerIndex];
                var previousLayer = Topology.Layers[layerIndex - 1];

                for (int neuronIndex = 0; neuronIndex < currentLayer.Neurons.Length; neuronIndex++)
                {
                    var neuron = currentLayer.Neurons[neuronIndex];
                    double sum = 0;

                    for (int prevNeuronIndex = 0; prevNeuronIndex < previousLayer.Neurons.Length; prevNeuronIndex++)
                    {
                        sum += previousLayer.Neurons[prevNeuronIndex].Value * 
                               neuron.Dendrites[prevNeuronIndex].Weight;
                    }

                    sum += neuron.Bias;
                    neuron.Value = ActivationFunctions.LeakyReLU(sum);
                }
            }

            // Output layer with Tanh for bounded output
            var outputLayer = Topology.Layers[^1];
            var outputs = new double[outputLayer.Neurons.Length];
            
            for (int i = 0; i < outputLayer.Neurons.Length; i++)
            {
                outputs[i] = ActivationFunctions.Tanh(outputLayer.Neurons[i].Value);
            }

            return outputs;
        }

        public double[] GetWeights()
        {
            var weights = new List<double>();
            
            for (int layerIndex = 1; layerIndex < Topology.Layers.Length; layerIndex++)
            {
                var layer = Topology.Layers[layerIndex];
                
                foreach (var neuron in layer.Neurons)
                {
                    weights.AddRange(neuron.Dendrites.Select(d => d.Weight));
                    weights.Add(neuron.Bias);
                }
            }

            return weights.ToArray();
        }

        public void SetWeights(double[] weights)
        {
            int weightIndex = 0;
            
            for (int layerIndex = 1; layerIndex < Topology.Layers.Length; layerIndex++)
            {
                var layer = Topology.Layers[layerIndex];
                
                foreach (var neuron in layer.Neurons)
                {
                    for (int i = 0; i < neuron.Dendrites.Length; i++)
                    {
                        neuron.Dendrites[i].Weight = weights[weightIndex++];
                    }
                    neuron.Bias = weights[weightIndex++];
                }
            }
        }

        public int GetTotalWeights() => GetWeights().Length;

        public void RandomizeWeights()
        {
            foreach (var layer in Topology.Layers.Skip(1))
            {
                foreach (var neuron in layer.Neurons)
                {
                    for (int i = 0; i < neuron.Dendrites.Length; i++)
                    {
                        neuron.Dendrites[i].Weight = GetRandomWeight(neuron.Dendrites.Length);
                    }
                    neuron.Bias = GetRandomWeight(neuron.Dendrites.Length);
                }
            }
        }

        private double GetRandomWeight(int inputCount) => 
            (_random.NextDouble() * 2 - 1) * Math.Sqrt(2.0 / inputCount); // Xavier initialization

        public void Dispose()
        {
            Topology?.Dispose();
        }
    }

    public class NetworkTopology : IDisposable
    {
        public Layer[] Layers { get; private set; }

        public NetworkTopology(int[] layerSizes, Random random)
        {
            Layers = new Layer[layerSizes.Length];
            
            // Input layer (no biases, no weights)
            Layers[0] = new Layer(layerSizes[0], 0, random);
            
            // Hidden and output layers
            for (int i = 1; i < layerSizes.Length; i++)
            {
                Layers[i] = new Layer(layerSizes[i], layerSizes[i - 1], random);
            }
        }

        public void Dispose()
        {
            foreach (var layer in Layers)
            {
                layer?.Dispose();
            }
        }
    }

    public class Layer : IDisposable
    {
        public Neuron[] Neurons { get; private set; }

        public Layer(int neuronCount, int inputCount, Random random)
        {
            Neurons = new Neuron[neuronCount];
            for (int i = 0; i < neuronCount; i++)
            {
                Neurons[i] = new Neuron(inputCount, random);
            }
        }

        public void Dispose()
        {
            foreach (var neuron in Neurons)
            {
                neuron?.Dispose();
            }
        }
    }

    public class Neuron : IDisposable
    {
        public Dendrite[] Dendrites { get; private set; }
        public double Bias { get; set; }
        public double Value { get; set; }
        public double Delta { get; set; }

        public Neuron(int inputCount, Random random)
        {
            if (inputCount > 0)
            {
                Dendrites = new Dendrite[inputCount];
                for (int i = 0; i < inputCount; i++)
                {
                    Dendrites[i] = new Dendrite { Weight = (random.NextDouble() * 2 - 1) * Math.Sqrt(2.0 / inputCount) };
                }
                Bias = (random.NextDouble() * 2 - 1) * Math.Sqrt(2.0 / inputCount);
            }
            else
            {
                Dendrites = Array.Empty<Dendrite>();
                Bias = 0;
            }
        }

        public void Dispose()
        {
            // Managed resources cleanup
        }
    }

    public struct Dendrite
    {
        public double Weight;
    }

    public static class ActivationFunctions
    {
        public static double ReLU(double x) => Math.Max(0, x);
        
        public static double LeakyReLU(double x) => x > 0 ? x : 0.01 * x;
        
        public static double Tanh(double x) => Math.Tanh(x);
        
        public static double Sigmoid(double x) => 1.0 / (1.0 + Math.Exp(-x));
    }
}