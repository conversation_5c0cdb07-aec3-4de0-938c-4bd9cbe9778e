{"format": 1, "restore": {"D:\\Hacker\\Documents\\cAlgo\\Sources\\Robots\\Genetic Trader ML\\Genetic Trader ML\\Genetic Trader ML.csproj": {}}, "projects": {"D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\SSL Channel\\SSL Channel\\SSL Channel.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\SSL Channel\\SSL Channel\\SSL Channel.csproj", "projectName": "SSL Channel", "projectPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\SSL Channel\\SSL Channel\\SSL Channel.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\SSL Channel\\SSL Channel\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"cTrader.Automate": {"target": "Package", "version": "[1.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\VWAP\\VWAP\\VWAP.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\VWAP\\VWAP\\VWAP.csproj", "projectName": "VWAP", "projectPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\VWAP\\VWAP\\VWAP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\VWAP\\VWAP\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"cTrader.Automate": {"target": "Package", "version": "[1.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\Hacker\\Documents\\cAlgo\\Sources\\Robots\\Genetic Trader ML\\Genetic Trader ML\\Genetic Trader ML.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Robots\\Genetic Trader ML\\Genetic Trader ML\\Genetic Trader ML.csproj", "projectName": "Genetic Trader ML", "projectPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Robots\\Genetic Trader ML\\Genetic Trader ML\\Genetic Trader ML.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Robots\\Genetic Trader ML\\Genetic Trader ML\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\SSL Channel\\SSL Channel\\SSL Channel.csproj": {"projectPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\SSL Channel\\SSL Channel\\SSL Channel.csproj"}, "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\VWAP\\VWAP\\VWAP.csproj": {"projectPath": "D:\\Hacker\\Documents\\cAlgo\\Sources\\Indicators\\VWAP\\VWAP\\VWAP.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"cTrader.Automate": {"target": "Package", "version": "[1.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.100\\RuntimeIdentifierGraph.json"}}}}}