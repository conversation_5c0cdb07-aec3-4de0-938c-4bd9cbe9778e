using System;
using System.Linq;

namespace GeneticTraderML.Services
{
    public class InputNormalizer
    {
        private readonly double[] _means;
        private readonly double[] _stdDevs;
        private readonly int _inputSize;

        public InputNormalizer(int inputSize)
        {
            _inputSize = inputSize;
            _means = new double[inputSize];
            _stdDevs = new double[inputSize];
        }

        public void CalculateStatistics(double[][] trainingData)
        {
            if (trainingData.Length == 0) return;

            // Calculate means
            for (int i = 0; i < _inputSize; i++)
            {
                _means[i] = trainingData.Average(sample => sample[i]);
            }

            // Calculate standard deviations
            for (int i = 0; i < _inputSize; i++)
            {
                var variance = trainingData.Average(sample => Math.Pow(sample[i] - _means[i], 2));
                _stdDevs[i] = Math.Sqrt(variance);
            }
        }

        public double[] Normalize(double[] inputs)
        {
            if (inputs.Length != _inputSize)
                throw new ArgumentException("Input size mismatch");

            var normalized = new double[_inputSize];
            
            for (int i = 0; i < _inputSize; i++)
            {
                // Z-score normalization with epsilon to avoid division by zero
                var stdDev = _stdDevs[i] == 0 ? 1 : _stdDevs[i];
                normalized[i] = (inputs[i] - _means[i]) / stdDev;
                
                // Clip extreme values
                normalized[i] = Math.Max(-3, Math.Min(3, normalized[i]));
            }

            return normalized;
        }

        public double[] Denormalize(double[] normalizedInputs)
        {
            var denormalized = new double[normalizedInputs.Length];
            
            for (int i = 0; i < normalizedInputs.Length; i++)
            {
                var stdDev = _stdDevs[i] == 0 ? 1 : _stdDevs[i];
                denormalized[i] = (normalizedInputs[i] * stdDev) + _means[i];
            }

            return denormalized;
        }
    }
}