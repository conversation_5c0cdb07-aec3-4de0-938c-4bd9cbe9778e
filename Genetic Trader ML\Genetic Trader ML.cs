using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using cAlgo.API;
using cAlgo.API.Collections;
using cAlgo.API.Indicators;
using cAlgo.API.Internals;
using GeneticTraderML.Models;
using GeneticTraderML.Algorithms;
using GeneticTraderML.Services;
using GeneticTraderML.NeuralNetworks;
using Newtonsoft.Json;

namespace cAlgo.Robots
{
    [Robot(AccessRights = AccessRights.FullAccess)]
    public class GeneticTraderML : Robot
    {
        [Parameter("Training Mode", DefaultValue = true)]
        public bool TrainingMode { get; set; }
        
        [Parameter("Population Size", DefaultValue = 50)]
        public int PopulationSize { get; set; }
        
        [Parameter("Risk Per Trade %", DefaultValue = 1.0)]
        public double RiskPerTrade { get; set; }
        
        [Parameter("Max Daily Loss %", DefaultValue = 2.0)]
        public double MaxDailyLoss { get; set; }

        private GeneticAlgorithm _geneticAlgorithm;
        private RiskManager _riskManager;
        private InputNormalizer _inputNormalizer;
        private List<Trader> _traders;
        
        // Indicators
        private AverageTrueRange _atr;
        private MoneyFlowIndex _mfi;
        private MovingAverage _fastMA, _slowMA;
        
        private string _dataFilePath;
        private bool _isInitialized = false;

        protected override void OnStart()
        {
            try
            {
                InitializeServices();
                LoadOrCreatePopulation();
                InitializeIndicators();
                
                _isInitialized = true;
                Print("Genetic Trader ML initialized successfully");
            }
            catch (Exception ex)
            {
                Print($"Initialization failed: {ex.Message}");
                Stop();
            }
        }

        private void InitializeServices()
        {
            // Validate required properties
            if (Symbol == null)
                throw new InvalidOperationException("Symbol is not available during initialization");

            if (string.IsNullOrEmpty(SymbolName))
                throw new InvalidOperationException("SymbolName is not available during initialization");

            _geneticAlgorithm = new GeneticAlgorithm(0.8, 0.1, 3);
            _riskManager = new RiskManager(Symbol, RiskPerTrade / 100, MaxDailyLoss / 100);
            _inputNormalizer = new InputNormalizer(20);

            //_dataFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),"cAlgo", "GeneticTraderML", $"population_{SymbolName}.json");

            _dataFilePath = @"C:\Users\<USER>\OneDrive\Documents\cAlgo\Sources\Robots\Genetic Trader ML\json-"+SymbolName+".txt";

            Print($"Services initialized. Data file path: {_dataFilePath}");
        }

        private void LoadOrCreatePopulation()
        {
            if (File.Exists(_dataFilePath) && !TrainingMode)
            {
                try
                {
                    var json = File.ReadAllText(_dataFilePath);
                    _traders = JsonConvert.DeserializeObject<List<Trader>>(json);

                    // Validate loaded traders
                    if (_traders == null || _traders.Count == 0)
                    {
                        Print("Loaded population is empty, creating new population");
                        CreateNewPopulation();
                    }
                    else
                    {
                        // Validate each trader has a brain
                        bool needsRecreation = false;
                        foreach (var trader in _traders)
                        {
                            if (trader?.Brain == null)
                            {
                                needsRecreation = true;
                                break;
                            }
                        }

                        if (needsRecreation)
                        {
                            Print("Some traders have invalid brains, creating new population");
                            CreateNewPopulation();
                        }
                        else
                        {
                            Print($"Loaded {_traders.Count} traders from file");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Print($"Failed to load population: {ex.Message}");
                    CreateNewPopulation();
                }
            }
            else
            {
                CreateNewPopulation();
            }
        }

        private void CreateNewPopulation()
        {
            Print("Creating new population");
            _traders = new List<Trader>();
            for (int i = 0; i < PopulationSize; i++)
            {
                _traders.Add(new Trader());
            }
            Print($"Created new population with {_traders.Count} traders");
        }

        private void InitializeIndicators()
        {
            // Validate we have enough historical data
            if (Bars == null)
                throw new InvalidOperationException("Bars data is not available");

            if (Bars.Count < 250)
            {
                Print($"Warning: Only {Bars.Count} bars available. Indicators may not be accurate initially.");
            }

            // Initialize indicators with null checks
            _atr = Indicators.AverageTrueRange(14, MovingAverageType.Simple);
            _mfi = Indicators.MoneyFlowIndex(14);
            _fastMA = Indicators.MovingAverage(Bars.ClosePrices, 20, MovingAverageType.Simple);
            _slowMA = Indicators.MovingAverage(Bars.ClosePrices, 200, MovingAverageType.Simple);

            // Validate indicators were created successfully
            if (_atr == null || _mfi == null || _fastMA == null || _slowMA == null)
                throw new InvalidOperationException("Failed to initialize one or more indicators");

            Print("Indicators initialized successfully");
        }

        protected override void OnBar()
        {
            if (!_isInitialized) return;

            try
            {
                var inputs = PrepareInputs();
                ProcessTradingDecisions(inputs);
                
                if (TrainingMode)
                {
                    EvolveIfNeeded();
                }
            }
            catch (Exception ex)
            {
                Print($"Error in OnBar: {ex.Message}");
            }
        }

        private double[] PrepareInputs()
        {
            try
            {
                // Validate required data
                if (Bars?.ClosePrices == null || Bars.Count < 15)
                {
                    Print("Error: Insufficient bar data for input preparation");
                    return new double[20]; // Return zero array as fallback
                }

                if (_mfi?.Result == null || _atr?.Result == null ||
                    _fastMA?.Result == null || _slowMA?.Result == null)
                {
                    Print("Error: One or more indicators not ready");
                    return new double[20]; // Return zero array as fallback
                }

                var inputs = new double[20];
                var index = 0;

                // Price momentum features with safety checks
                var close1 = Bars.ClosePrices.Last(1);
                var close2 = Bars.ClosePrices.Last(2);
                var close5 = Bars.ClosePrices.Last(5);
                var close10 = Bars.ClosePrices.Last(10);

                inputs[index++] = close1 > 0 && close2 > 0 ? Math.Log(close1) - Math.Log(close2) : 0;
                inputs[index++] = close1 > 0 && close5 > 0 ? Math.Log(close1) - Math.Log(close5) : 0;
                inputs[index++] = close1 > 0 && close10 > 0 ? Math.Log(close1) - Math.Log(close10) : 0;

                // Volume and momentum
                inputs[index++] = double.IsNaN(_mfi.Result.LastValue) ? 0.5 : _mfi.Result.LastValue / 100;

                var vol1 = Bars.TickVolumes.Last(1);
                var vol2 = Bars.TickVolumes.Last(2);
                inputs[index++] = vol1 > 0 && vol2 > 0 ? Math.Log(vol1) - Math.Log(vol2) : 0;

                // Volatility
                var high1 = Bars.HighPrices.Last(1);
                var low1 = Bars.LowPrices.Last(1);
                inputs[index++] = high1 > 0 && low1 > 0 ? Math.Log(high1) - Math.Log(low1) : 0;
                inputs[index++] = double.IsNaN(_atr.Result.LastValue) ? 0 : _atr.Result.LastValue / Symbol.PipSize / 100;

                // Trend features
                var fastMA = _fastMA.Result.LastValue;
                var slowMA = _slowMA.Result.LastValue;
                inputs[index++] = fastMA > 0 && slowMA > 0 && !double.IsNaN(fastMA) && !double.IsNaN(slowMA) ?
                                 Math.Log(fastMA) - Math.Log(slowMA) : 0;

                // Time features
                inputs[index++] = (double)Bars.OpenTimes.Last(0).DayOfWeek / 5;
                inputs[index++] = (double)Bars.OpenTimes.Last(0).Hour / 24;

                // Fill remaining inputs with technical features
                for (int i = index; i < inputs.Length; i++)
                {
                    var lookback = i + 2;
                    if (Bars.Count > lookback)
                    {
                        var closeLookback = Bars.ClosePrices.Last(lookback);
                        inputs[i] = close1 > 0 && closeLookback > 0 ?
                                   (Math.Log(close1) - Math.Log(closeLookback)) * 0.1 : 0;
                    }
                    else
                    {
                        inputs[i] = 0;
                    }
                }

                return _inputNormalizer.Normalize(inputs);
            }
            catch (Exception ex)
            {
                Print($"Error preparing inputs: {ex.Message}");
                return new double[20]; // Return zero array as fallback
            }
        }

        private void ProcessTradingDecisions(double[] inputs)
        {
            // Validate inputs and traders
            if (inputs == null)
            {
                Print("Error: Inputs are null");
                return;
            }

            if (_traders == null || _traders.Count == 0)
            {
                Print("Error: No traders available");
                return;
            }

            // Use best trader for live trading
            var bestTrader = _traders.OrderByDescending(t => t.Fitness).FirstOrDefault();
            if (bestTrader?.Brain == null)
            {
                Print("Error: Best trader or its brain is null");
                return;
            }

            var networkOutput = bestTrader.Think(inputs);
            if (networkOutput == null)
            {
                Print("Error: Network output is null");
                return;
            }

            var decision = bestTrader.InterpretOutput(networkOutput);
            ExecuteTradingDecision(decision, bestTrader);

            if (TrainingMode)
            {
                TrainTraders(inputs);
            }
        }

        private void ExecuteTradingDecision(TradingDecision decision, Trader trader)
        {
            switch (decision)
            {
                case TradingDecision.Buy:
                    if (_riskManager.CanOpenPosition(Account.Equity, Account.Equity))
                    {
                        ExecuteBuyOrder(trader);
                    }
                    break;
                    
                case TradingDecision.Sell:
                    if (_riskManager.CanOpenPosition(Account.Equity, Account.Equity))
                    {
                        ExecuteSellOrder(trader);
                    }
                    break;
                    
                case TradingDecision.CloseAll:
                    CloseAllPositions();
                    break;
            }
        }

        private void ExecuteBuyOrder(Trader trader)
        {
            var stopLossPips = _riskManager.GetStopLossPips(TradeType.Buy, _atr.Result.LastValue, Symbol.Ask);
            var takeProfitPips = _riskManager.GetTakeProfitPips(TradeType.Buy, stopLossPips);
            var volume = _riskManager.CalculatePositionSize(Account.Equity, _atr.Result.LastValue, stopLossPips);

            ExecuteMarketOrderAsync(TradeType.Buy, SymbolName, volume, "GeneticLong", 
                                  stopLossPips, takeProfitPips);
        }

        private void ExecuteSellOrder(Trader trader)
        {
            var stopLossPips = _riskManager.GetStopLossPips(TradeType.Sell, _atr.Result.LastValue, Symbol.Bid);
            var takeProfitPips = _riskManager.GetTakeProfitPips(TradeType.Sell, stopLossPips);
            var volume = _riskManager.CalculatePositionSize(Account.Equity, _atr.Result.LastValue, stopLossPips);

            ExecuteMarketOrderAsync(TradeType.Sell, SymbolName, volume, "GeneticShort",
                                  stopLossPips, takeProfitPips);
        }

        private void CloseAllPositions()
        {
            try
            {
                // Close all positions for this symbol
                var positions = Positions.FindAll("GeneticLong", SymbolName)
                    .Concat(Positions.FindAll("GeneticShort", SymbolName))
                    .ToList();

                if (positions.Count == 0)
                {
                    Print("No positions to close");
                    return;
                }

                foreach (var position in positions)
                {
                    ClosePositionAsync(position);
                }

                Print($"Closing {positions.Count} positions");
            }
            catch (Exception ex)
            {
                Print($"Error closing positions: {ex.Message}");
            }
        }

        private void TrainTraders(double[] inputs)
        {
            // Simulated training for all traders
            foreach (var trader in _traders)
            {
                var output = trader.Think(inputs);
                var decision = trader.InterpretOutput(output);
                
                // Simulate P&L based on market movement
                var marketMove = (Bars.ClosePrices.Last(1) - Bars.ClosePrices.Last(2)) / Bars.ClosePrices.Last(2);
                var simulatedPnl = SimulateTradePnl(decision, marketMove, trader);
                
                trader.UpdateBalance(simulatedPnl);
                trader.CalculateFitness();
            }
        }

        private double SimulateTradePnl(TradingDecision decision, double marketMove, Trader trader)
        {
            // Simplified P&L simulation for training
            const double tradeSize = 1000;
            
            return decision switch
            {
                TradingDecision.Buy when marketMove > 0 => tradeSize * marketMove * 10,
                TradingDecision.Buy when marketMove < 0 => tradeSize * marketMove * 10,
                TradingDecision.Sell when marketMove < 0 => tradeSize * Math.Abs(marketMove) * 10,
                TradingDecision.Sell when marketMove > 0 => -tradeSize * marketMove * 10,
                _ => -1 // Small cost for inactive trading
            };
        }

        private void EvolveIfNeeded()
        {
            // Evolve every 100 bars or if population quality drops
            if (Bars.Count % 100 == 0)
            {
                _geneticAlgorithm.Evolve(_traders);
                Print($"Generation {_geneticAlgorithm.Generation} evolved. Best fitness: {_geneticAlgorithm.BestFitness:F4}");
            }
        }

        protected override void OnStop()
        {
            if (_isInitialized)
            {
                SavePopulation();
                CleanupResources();
            }
        }

        private async void SavePopulation()
        {
            try
            {
                var directory = Path.GetDirectoryName(_dataFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Only save best performers
                var bestTraders = _traders.OrderByDescending(t => t.Fitness)
                                         .Take(PopulationSize / 2)
                                         .ToList();

                var json = JsonConvert.SerializeObject(bestTraders, Formatting.Indented);
                await File.WriteAllTextAsync(_dataFilePath, json);
                
                Print($"Saved {bestTraders.Count} best traders to file");
            }
            catch (Exception ex)
            {
                Print($"Failed to save population: {ex.Message}");
            }
        }

        private void CleanupResources()
        {
            foreach (var trader in _traders)
            {
                trader.Brain?.Dispose();
            }
            _traders?.Clear();
        }
    }
}