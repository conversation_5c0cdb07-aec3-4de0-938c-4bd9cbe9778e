using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using cAlgo.API;
using cAlgo.API.Collections;
using cAlgo.API.Indicators;
using cAlgo.API.Internals;
using GeneticTraderML.Models;
using GeneticTraderML.Algorithms;
using GeneticTraderML.Services;
using Newtonsoft.Json;

namespace cAlgo.Robots
{
    [Robot(AccessRights = AccessRights.FullAccess)]
    public class GeneticTraderML : Robot
    {
        [Parameter("Training Mode", DefaultValue = true)]
        public bool TrainingMode { get; set; }
        
        [Parameter("Population Size", DefaultValue = 50)]
        public int PopulationSize { get; set; }
        
        [Parameter("Risk Per Trade %", DefaultValue = 1.0)]
        public double RiskPerTrade { get; set; }
        
        [Parameter("Max Daily Loss %", DefaultValue = 2.0)]
        public double MaxDailyLoss { get; set; }

        private GeneticAlgorithm _geneticAlgorithm;
        private RiskManager _riskManager;
        private InputNormalizer _inputNormalizer;
        private List<Trader> _traders;
        
        // Indicators
        private AverageTrueRange _atr;
        private MoneyFlowIndex _mfi;
        private MovingAverage _fastMA, _slowMA;
        
        private string _dataFilePath;
        private bool _isInitialized = false;

        protected override void OnStart()
        {
            try
            {
                InitializeServices();
                LoadOrCreatePopulation();
                InitializeIndicators();
                
                _isInitialized = true;
                Print("Genetic Trader ML initialized successfully");
            }
            catch (Exception ex)
            {
                Print($"Initialization failed: {ex.Message}");
                Stop();
            }
        }

        private void InitializeServices()
        {
            _geneticAlgorithm = new GeneticAlgorithm(0.8, 0.1, 3);
            _riskManager = new RiskManager(Symbol, RiskPerTrade / 100, MaxDailyLoss / 100);
            _inputNormalizer = new InputNormalizer(20);
            
            //_dataFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),"cAlgo", "GeneticTraderML", $"population_{SymbolName}.json");

            _dataFilePath = @"C:\Users\<USER>\OneDrive\Documents\cAlgo\Sources\Robots\Genetic Trader ML\json-"+SymbolName+".txt";
        }

        private void LoadOrCreatePopulation()
        {
            if (File.Exists(_dataFilePath) && !TrainingMode)
            {
                try
                {
                    var json = File.ReadAllText(_dataFilePath);
                    _traders = JsonConvert.DeserializeObject<List<Trader>>(json);
                    Print($"Loaded {_traders.Count} traders from file");
                }
                catch (Exception ex)
                {
                    Print($"Failed to load population: {ex.Message}");
                    CreateNewPopulation();
                }
            }
            else
            {
                CreateNewPopulation();
            }
        }

        private void CreateNewPopulation()
        {
            Pr
            _traders = new List<Trader>();
            for (int i = 0; i < PopulationSize; i++)
            {
                _traders.Add(new Trader());
            }
            Print($"Created new population with {_traders.Count} traders");
        }

        private void InitializeIndicators()
        {
            _atr = Indicators.AverageTrueRange(14, MovingAverageType.Simple);
            _mfi = Indicators.MoneyFlowIndex(14);
            _fastMA = Indicators.MovingAverage(Bars.ClosePrices, 20, MovingAverageType.Simple);
            _slowMA = Indicators.MovingAverage(Bars.ClosePrices, 200, MovingAverageType.Simple);
        }

        protected override void OnBar()
        {
            if (!_isInitialized) return;

            try
            {
                var inputs = PrepareInputs();
                ProcessTradingDecisions(inputs);
                
                if (TrainingMode)
                {
                    EvolveIfNeeded();
                }
            }
            catch (Exception ex)
            {
                Print($"Error in OnBar: {ex.Message}");
            }
        }

        private double[] PrepareInputs()
        {
            var inputs = new double[20];
            var index = 0;

            // Price momentum features
            inputs[index++] = Math.Log(Bars.ClosePrices.Last(1)) - Math.Log(Bars.ClosePrices.Last(2));
            inputs[index++] = Math.Log(Bars.ClosePrices.Last(1)) - Math.Log(Bars.ClosePrices.Last(5));
            inputs[index++] = Math.Log(Bars.ClosePrices.Last(1)) - Math.Log(Bars.ClosePrices.Last(10));
            
            // Volume and momentum
            inputs[index++] = _mfi.Result.LastValue / 100;
            inputs[index++] = Math.Log(Bars.TickVolumes.Last(1)) - Math.Log(Bars.TickVolumes.Last(2));
            
            // Volatility
            inputs[index++] = Math.Log(Bars.HighPrices.Last(1)) - Math.Log(Bars.LowPrices.Last(1));
            inputs[index++] = _atr.Result.LastValue / Symbol.PipSize / 100;
            
            // Trend features
            inputs[index++] = Math.Log(_fastMA.Result.LastValue) - Math.Log(_slowMA.Result.LastValue);
            
            // Time features
            inputs[index++] = (double)Bars.OpenTimes.Last(0).DayOfWeek / 5;
            inputs[index++] = (double)Bars.OpenTimes.Last(0).Hour / 24;

            // Fill remaining inputs with technical features
            for (int i = index; i < inputs.Length; i++)
            {
                inputs[i] = (Math.Log(Bars.ClosePrices.Last(1)) - Math.Log(Bars.ClosePrices.Last(i + 2))) * 0.1;
            }

            return _inputNormalizer.Normalize(inputs);
        }

        private void ProcessTradingDecisions(double[] inputs)
        {
            // Use best trader for live trading
            var bestTrader = _traders.OrderByDescending(t => t.Fitness).First();
            var networkOutput = bestTrader.Think(inputs);
            var decision = bestTrader.InterpretOutput(networkOutput);

            ExecuteTradingDecision(decision, bestTrader);

            if (TrainingMode)
            {
                TrainTraders(inputs);
            }
        }

        private void ExecuteTradingDecision(TradingDecision decision, Trader trader)
        {
            switch (decision)
            {
                case TradingDecision.Buy:
                    if (_riskManager.CanOpenPosition(Account.Equity, Account.Equity))
                    {
                        ExecuteBuyOrder(trader);
                    }
                    break;
                    
                case TradingDecision.Sell:
                    if (_riskManager.CanOpenPosition(Account.Equity, Account.Equity))
                    {
                        ExecuteSellOrder(trader);
                    }
                    break;
                    
                case TradingDecision.CloseAll:
                    CloseAllPositions();
                    break;
            }
        }

        private void ExecuteBuyOrder(Trader trader)
        {
            var stopLossPips = _riskManager.GetStopLossPips(TradeType.Buy, _atr.Result.LastValue, Symbol.Ask);
            var takeProfitPips = _riskManager.GetTakeProfitPips(TradeType.Buy, stopLossPips);
            var volume = _riskManager.CalculatePositionSize(Account.Equity, _atr.Result.LastValue, stopLossPips);

            ExecuteMarketOrderAsync(TradeType.Buy, SymbolName, volume, "GeneticLong", 
                                  stopLossPips, takeProfitPips);
        }

        private void ExecuteSellOrder(Trader trader)
        {
            var stopLossPips = _riskManager.GetStopLossPips(TradeType.Sell, _atr.Result.LastValue, Symbol.Bid);
            var takeProfitPips = _riskManager.GetTakeProfitPips(TradeType.Sell, stopLossPips);
            var volume = _riskManager.CalculatePositionSize(Account.Equity, _atr.Result.LastValue, stopLossPips);

            ExecuteMarketOrderAsync(TradeType.Sell, SymbolName, volume, "GeneticShort",
                                  stopLossPips, takeProfitPips);
        }

        private void CloseAllPositions()
        {
            try
            {
                // Close all positions for this symbol
                var positions = Positions.FindAll("GeneticLong", SymbolName)
                    .Concat(Positions.FindAll("GeneticShort", SymbolName))
                    .ToList();

                if (positions.Count == 0)
                {
                    Print("No positions to close");
                    return;
                }

                foreach (var position in positions)
                {
                    ClosePositionAsync(position);
                }

                Print($"Closing {positions.Count} positions");
            }
            catch (Exception ex)
            {
                Print($"Error closing positions: {ex.Message}");
            }
        }

        private void TrainTraders(double[] inputs)
        {
            // Simulated training for all traders
            foreach (var trader in _traders)
            {
                var output = trader.Think(inputs);
                var decision = trader.InterpretOutput(output);
                
                // Simulate P&L based on market movement
                var marketMove = (Bars.ClosePrices.Last(1) - Bars.ClosePrices.Last(2)) / Bars.ClosePrices.Last(2);
                var simulatedPnl = SimulateTradePnl(decision, marketMove, trader);
                
                trader.UpdateBalance(simulatedPnl);
                trader.CalculateFitness();
            }
        }

        private double SimulateTradePnl(TradingDecision decision, double marketMove, Trader trader)
        {
            // Simplified P&L simulation for training
            const double tradeSize = 1000;
            
            return decision switch
            {
                TradingDecision.Buy when marketMove > 0 => tradeSize * marketMove * 10,
                TradingDecision.Buy when marketMove < 0 => tradeSize * marketMove * 10,
                TradingDecision.Sell when marketMove < 0 => tradeSize * Math.Abs(marketMove) * 10,
                TradingDecision.Sell when marketMove > 0 => -tradeSize * marketMove * 10,
                _ => -1 // Small cost for inactive trading
            };
        }

        private void EvolveIfNeeded()
        {
            // Evolve every 100 bars or if population quality drops
            if (Bars.Count % 100 == 0)
            {
                _geneticAlgorithm.Evolve(_traders);
                Print($"Generation {_geneticAlgorithm.Generation} evolved. Best fitness: {_geneticAlgorithm.BestFitness:F4}");
            }
        }

        protected override void OnStop()
        {
            if (_isInitialized)
            {
                SavePopulation();
                CleanupResources();
            }
        }

        private async void SavePopulation()
        {
            try
            {
                var directory = Path.GetDirectoryName(_dataFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Only save best performers
                var bestTraders = _traders.OrderByDescending(t => t.Fitness)
                                         .Take(PopulationSize / 2)
                                         .ToList();

                var json = JsonConvert.SerializeObject(bestTraders, Formatting.Indented);
                await File.WriteAllTextAsync(_dataFilePath, json);
                
                Print($"Saved {bestTraders.Count} best traders to file");
            }
            catch (Exception ex)
            {
                Print($"Failed to save population: {ex.Message}");
            }
        }

        private void CleanupResources()
        {
            foreach (var trader in _traders)
            {
                trader.Brain?.Dispose();
            }
            _traders?.Clear();
        }
    }
}